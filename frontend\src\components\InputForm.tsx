import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>Pen, Brain, Send, StopCircle, Zap, Cpu } from "lucide-react";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// Updated InputFormProps
interface InputFormProps {
  onSubmit: (inputValue: string, effort: string, model: string) => void;
  onCancel: () => void;
  isLoading: boolean;
  hasHistory: boolean;
}

export const InputForm: React.FC<InputFormProps> = ({
  onSubmit,
  onCancel,
  isLoading,
  hasHistory,
}) => {
  const [internalInputValue, setInternalInputValue] = useState("");
  const [effort, setEffort] = useState("medium");
  const [model, setModel] = useState("gemini-2.5-flash-preview-04-17");

  const handleInternalSubmit = (e?: React.FormEvent) => {
    if (e) e.preventDefault();
    if (!internalInputValue.trim()) return;
    onSubmit(internalInputValue, effort, model);
    setInternalInputValue("");
  };

  const handleInternalKeyDown = (
    e: React.KeyboardEvent<HTMLTextAreaElement>
  ) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleInternalSubmit();
    }
  };

  const isSubmitDisabled = !internalInputValue.trim() || isLoading;

  return (
    <div className="border-t border-neutral-700/50 bg-gradient-to-t from-neutral-800 to-neutral-800/95 backdrop-blur-sm">
      <form
        onSubmit={handleInternalSubmit}
        className="flex flex-col gap-4 p-4 md:p-6 max-w-5xl mx-auto"
      >
        {/* Main Input Container */}
        <div className="relative group">
          <div className="flex flex-row items-center justify-between text-white rounded-2xl bg-gradient-to-br from-neutral-700 to-neutral-800 border border-neutral-600/50 shadow-lg hover:shadow-xl transition-all duration-300 px-5 py-4 min-h-[72px]">
            <div className="absolute inset-0 bg-gradient-to-br from-neutral-600/10 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            <Textarea
              value={internalInputValue}
              onChange={(e) => setInternalInputValue(e.target.value)}
              onKeyDown={handleInternalKeyDown}
              placeholder="Ask me anything... I'll research it for you!"
              className="w-full text-neutral-100 placeholder-neutral-400 resize-none border-0 focus:outline-none focus:ring-0 outline-none focus-visible:ring-0 shadow-none bg-transparent text-base min-h-[40px] max-h-[200px] relative z-10"
              rows={1}
            />
            <div className="flex items-center gap-2 relative z-10">
              {isLoading ? (
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className="text-red-400 hover:text-red-300 hover:bg-red-500/20 p-3 rounded-xl transition-all duration-200 shadow-lg hover:shadow-red-500/25"
                  onClick={onCancel}
                >
                  <StopCircle className="h-5 w-5" />
                </Button>
              ) : (
                <Button
                  type="submit"
                  variant="ghost"
                  className={`${
                    isSubmitDisabled
                      ? "text-neutral-500 cursor-not-allowed"
                      : "text-blue-400 hover:text-blue-300 hover:bg-blue-500/20 shadow-lg hover:shadow-blue-500/25"
                  } px-4 py-3 rounded-xl transition-all duration-200 font-medium gap-2`}
                  disabled={isSubmitDisabled}
                >
                  <Send className="h-4 w-4" />
                  Search
                </Button>
              )}
            </div>
          </div>
        </div>
        {/* Controls Section */}
        <div className="flex items-center justify-between animate-fade-in">
          <div className="flex flex-row gap-3">
            {/* Effort Selector */}
            <div className="flex flex-row items-center gap-2 bg-gradient-to-br from-neutral-700 to-neutral-800 border border-neutral-600/50 text-neutral-300 rounded-xl px-3 py-2 shadow-md hover:shadow-lg transition-all duration-200 group">
              <div className="flex flex-row items-center text-sm font-medium">
                <Brain className="h-4 w-4 mr-2 text-amber-400 group-hover:text-amber-300 transition-colors duration-200" />
                <span className="text-neutral-200 group-hover:text-white transition-colors duration-200">Effort</span>
              </div>
              <Select value={effort} onValueChange={setEffort}>
                <SelectTrigger className="w-[100px] bg-transparent border-none cursor-pointer text-neutral-200 hover:text-white transition-colors duration-200">
                  <SelectValue placeholder="Effort" />
                </SelectTrigger>
                <SelectContent className="bg-gradient-to-br from-neutral-700 to-neutral-800 border border-neutral-600/50 text-neutral-300 shadow-xl">
                  <SelectItem
                    value="low"
                    className="hover:bg-neutral-600/50 focus:bg-neutral-600/50 cursor-pointer transition-colors duration-200"
                  >
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-green-400" />
                      Low
                    </div>
                  </SelectItem>
                  <SelectItem
                    value="medium"
                    className="hover:bg-neutral-600/50 focus:bg-neutral-600/50 cursor-pointer transition-colors duration-200"
                  >
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-yellow-400" />
                      Medium
                    </div>
                  </SelectItem>
                  <SelectItem
                    value="high"
                    className="hover:bg-neutral-600/50 focus:bg-neutral-600/50 cursor-pointer transition-colors duration-200"
                  >
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-red-400" />
                      High
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
            {/* Model Selector */}
            <div className="flex flex-row items-center gap-2 bg-gradient-to-br from-neutral-700 to-neutral-800 border border-neutral-600/50 text-neutral-300 rounded-xl px-3 py-2 shadow-md hover:shadow-lg transition-all duration-200 group">
              <div className="flex flex-row items-center text-sm font-medium">
                <Cpu className="h-4 w-4 mr-2 text-purple-400 group-hover:text-purple-300 transition-colors duration-200" />
                <span className="text-neutral-200 group-hover:text-white transition-colors duration-200">Model</span>
              </div>
              <Select value={model} onValueChange={setModel}>
                <SelectTrigger className="w-[130px] bg-transparent border-none cursor-pointer text-neutral-200 hover:text-white transition-colors duration-200">
                  <SelectValue placeholder="Model" />
                </SelectTrigger>
                <SelectContent className="bg-gradient-to-br from-neutral-700 to-neutral-800 border border-neutral-600/50 text-neutral-300 shadow-xl">
                  <SelectItem
                    value="gemini-2.0-flash"
                    className="hover:bg-neutral-600/50 focus:bg-neutral-600/50 cursor-pointer transition-colors duration-200"
                  >
                    <div className="flex items-center gap-2">
                      <Zap className="h-4 w-4 text-yellow-400" />
                      <span>2.0 Flash</span>
                    </div>
                  </SelectItem>
                  <SelectItem
                    value="gemini-2.5-flash-preview-04-17"
                    className="hover:bg-neutral-600/50 focus:bg-neutral-600/50 cursor-pointer transition-colors duration-200"
                  >
                    <div className="flex items-center gap-2">
                      <Zap className="h-4 w-4 text-orange-400" />
                      <span>2.5 Flash</span>
                    </div>
                  </SelectItem>
                  <SelectItem
                    value="gemini-2.5-pro-preview-05-06"
                    className="hover:bg-neutral-600/50 focus:bg-neutral-600/50 cursor-pointer transition-colors duration-200"
                  >
                    <div className="flex items-center gap-2">
                      <Cpu className="h-4 w-4 text-purple-400" />
                      <span>2.5 Pro</span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* New Search Button */}
          {hasHistory && (
            <Button
              className="bg-gradient-to-br from-neutral-700 to-neutral-800 border border-neutral-600/50 text-neutral-300 hover:text-white cursor-pointer rounded-xl px-4 py-2 shadow-md hover:shadow-lg transition-all duration-200 gap-2 font-medium group"
              variant="outline"
              onClick={() => window.location.reload()}
            >
              <SquarePen className="h-4 w-4 text-emerald-400 group-hover:text-emerald-300 transition-colors duration-200" />
              New Search
            </Button>
          )}
        </div>
      </form>
    </div>
  );
};
