import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
} from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Loader2,
  Activity,
  Info,
  Search,
  TextSearch,
  Brain,
  Pen,
  ChevronDown,
  ChevronUp,
  Clock,
  Zap,
} from "lucide-react";
import { useEffect, useState } from "react";

export interface ProcessedEvent {
  title: string;
  data: any;
  timestamp?: string;
  status?: 'pending' | 'active' | 'completed' | 'error';
}

interface ActivityTimelineProps {
  processedEvents: ProcessedEvent[];
  isLoading: boolean;
}

export function ActivityTimeline({
  processedEvents,
  isLoading,
}: ActivityTimelineProps) {
  const [isTimelineCollapsed, setIsTimelineCollapsed] =
    useState<boolean>(false);

  const getEventIcon = (title: string, index: number) => {
    if (index === 0 && isLoading && processedEvents.length === 0) {
      return <Loader2 className="h-4 w-4 text-blue-400 animate-spin" />;
    }
    if (title.toLowerCase().includes("generating")) {
      return <TextSearch className="h-4 w-4 text-purple-400" />;
    } else if (title.toLowerCase().includes("thinking")) {
      return <Loader2 className="h-4 w-4 text-blue-400 animate-spin" />;
    } else if (title.toLowerCase().includes("reflection")) {
      return <Brain className="h-4 w-4 text-amber-400" />;
    } else if (title.toLowerCase().includes("research")) {
      return <Search className="h-4 w-4 text-cyan-400" />;
    } else if (title.toLowerCase().includes("finalizing")) {
      return <Pen className="h-4 w-4 text-emerald-400" />;
    }
    return <Activity className="h-4 w-4 text-neutral-400" />;
  };



  useEffect(() => {
    if (!isLoading && processedEvents.length !== 0) {
      setIsTimelineCollapsed(true);
    }
  }, [isLoading, processedEvents]);

  const completedCount = processedEvents.filter((_, index) =>
    index < processedEvents.length - 1 || !isLoading
  ).length;

  return (
    <Card className="border-none rounded-xl bg-gradient-to-br from-neutral-700 to-neutral-800 shadow-lg backdrop-blur-sm max-h-96 transition-all duration-300 hover:shadow-xl">
      <CardHeader className="pb-3">
        <CardDescription className="flex items-center justify-between">
          <div
            className="flex items-center justify-start text-sm w-full cursor-pointer gap-3 text-neutral-100 hover:text-white transition-colors duration-200 group"
            onClick={() => setIsTimelineCollapsed(!isTimelineCollapsed)}
          >
            <div className="flex items-center gap-2">
              <Zap className="h-4 w-4 text-blue-400" />
              <span className="font-medium">Research Progress</span>
            </div>
            <div className="flex items-center gap-2 ml-auto">
              {processedEvents.length > 0 && (
                <div className="flex items-center gap-1 text-xs bg-neutral-600/50 px-2 py-1 rounded-full">
                  <Clock className="h-3 w-3" />
                  <span>{completedCount}/{processedEvents.length}</span>
                </div>
              )}
              {isTimelineCollapsed ? (
                <ChevronDown className="h-4 w-4 transition-transform duration-200 group-hover:scale-110" />
              ) : (
                <ChevronUp className="h-4 w-4 transition-transform duration-200 group-hover:scale-110" />
              )}
            </div>
          </div>
        </CardDescription>
      </CardHeader>
      <div className={`transition-all duration-300 ease-in-out overflow-hidden ${
        isTimelineCollapsed ? 'max-h-0 opacity-0' : 'max-h-96 opacity-100'
      }`}>
        <ScrollArea className="max-h-80 overflow-y-auto">
          <CardContent className="pt-0">
            {isLoading && processedEvents.length === 0 && (
              <div className="relative pl-10 pb-6 animate-fade-in">
                <div className="absolute left-4 top-4 h-full w-0.5 bg-gradient-to-b from-blue-400/50 to-transparent" />
                <div className="absolute left-1 top-2 h-7 w-7 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center ring-4 ring-blue-500/20 shadow-lg">
                  <Loader2 className="h-4 w-4 text-white animate-spin" />
                </div>
                <div className="ml-2">
                  <p className="text-sm text-neutral-200 font-semibold mb-1">
                    Initializing Research...
                  </p>
                  <p className="text-xs text-neutral-400">
                    Preparing to analyze your query
                  </p>
                </div>
              </div>
            )}
            {processedEvents.length > 0 ? (
              <div className="space-y-1">
                {processedEvents.map((eventItem, index) => (
                    <div key={index} className="relative pl-8 pb-4">
                      {index < processedEvents.length - 1 ||
                      (isLoading && index === processedEvents.length - 1) ? (
                        <div className="absolute left-3 top-3.5 h-full w-0.5 bg-neutral-600" />
                      ) : null}

                      <div className="absolute left-0.5 top-2 h-6 w-6 rounded-full bg-neutral-600 flex items-center justify-center ring-4 ring-neutral-700">
                        {getEventIcon(eventItem.title, index)}
                      </div>

                      <div>
                        <p className="text-sm text-neutral-200 font-medium mb-0.5">
                          {eventItem.title}
                        </p>
                        <p className="text-xs text-neutral-300 leading-relaxed">
                          {typeof eventItem.data === "string"
                            ? eventItem.data
                            : Array.isArray(eventItem.data)
                            ? (eventItem.data as string[]).join(", ")
                            : JSON.stringify(eventItem.data)}
                        </p>
                      </div>
                    </div>
                  ))}
                {isLoading && processedEvents.length > 0 && (
                  <div className="relative pl-10 pb-6 animate-fade-in">
                    <div className="absolute left-1 top-2 h-7 w-7 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center ring-4 ring-blue-500/20 shadow-lg animate-pulse">
                      <Loader2 className="h-4 w-4 text-white animate-spin" />
                    </div>
                    <div className="ml-2">
                      <p className="text-sm text-neutral-200 font-semibold mb-1">
                        Processing...
                      </p>
                      <p className="text-xs text-neutral-400">
                        Analyzing and gathering information
                      </p>
                    </div>
                  </div>
                )}
              </div>
            ) : !isLoading ? ( // Only show "No activity" if not loading and no events
              <div className="flex flex-col items-center justify-center h-full text-neutral-500 py-12 animate-fade-in">
                <div className="relative">
                  <Info className="h-8 w-8 mb-4 text-neutral-400" />
                  <div className="absolute -top-1 -right-1 h-3 w-3 rounded-full bg-blue-400 animate-ping" />
                </div>
                <p className="text-sm font-medium text-neutral-300 mb-1">No activity yet</p>
                <p className="text-xs text-neutral-500 text-center max-w-48">
                  Timeline will show research progress when processing begins
                </p>
              </div>
            ) : null}
          </CardContent>
        </ScrollArea>
      </div>
    </Card>
  );
}
