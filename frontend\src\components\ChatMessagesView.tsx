import type React from "react";
import type { Message } from "@langchain/langgraph-sdk";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Loader2, <PERSON><PERSON>, Copy<PERSON>heck } from "lucide-react";
import { InputForm } from "@/components/InputForm";
import { But<PERSON> } from "@/components/ui/button";
import { useState, ReactNode } from "react";
import ReactMarkdown from "react-markdown";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import {
  ActivityTimeline,
  ProcessedEvent,
} from "@/components/ActivityTimeline"; // Assuming ActivityTimeline is in the same dir or adjust path

// Markdown component props type from former ReportView
type MdComponentProps = {
  className?: string;
  children?: ReactNode;
  [key: string]: any;
};

// Markdown components (from former ReportView.tsx)
const mdComponents = {
  h1: ({ className, children, ...props }: MdComponentProps) => (
    <h1 className={cn("text-2xl font-bold mt-6 mb-3 text-neutral-100 border-b border-neutral-700/50 pb-2", className)} {...props}>
      {children}
    </h1>
  ),
  h2: ({ className, children, ...props }: MdComponentProps) => (
    <h2 className={cn("text-xl font-bold mt-5 mb-3 text-neutral-100", className)} {...props}>
      {children}
    </h2>
  ),
  h3: ({ className, children, ...props }: MdComponentProps) => (
    <h3 className={cn("text-lg font-semibold mt-4 mb-2 text-neutral-200", className)} {...props}>
      {children}
    </h3>
  ),
  p: ({ className, children, ...props }: MdComponentProps) => (
    <p className={cn("mb-4 leading-7 text-neutral-200", className)} {...props}>
      {children}
    </p>
  ),
  a: ({ className, children, href, ...props }: MdComponentProps) => (
    <Badge className="text-xs mx-0.5 bg-blue-500/20 hover:bg-blue-500/30 border-blue-500/30 transition-colors duration-200">
      <a
        className={cn("text-blue-300 hover:text-blue-200 text-xs transition-colors duration-200", className)}
        href={href}
        target="_blank"
        rel="noopener noreferrer"
        {...props}
      >
        {children}
      </a>
    </Badge>
  ),
  ul: ({ className, children, ...props }: MdComponentProps) => (
    <ul className={cn("list-disc pl-6 mb-3", className)} {...props}>
      {children}
    </ul>
  ),
  ol: ({ className, children, ...props }: MdComponentProps) => (
    <ol className={cn("list-decimal pl-6 mb-3", className)} {...props}>
      {children}
    </ol>
  ),
  li: ({ className, children, ...props }: MdComponentProps) => (
    <li className={cn("mb-1", className)} {...props}>
      {children}
    </li>
  ),
  blockquote: ({ className, children, ...props }: MdComponentProps) => (
    <blockquote
      className={cn(
        "border-l-4 border-neutral-600 pl-4 italic my-3 text-sm",
        className
      )}
      {...props}
    >
      {children}
    </blockquote>
  ),
  code: ({ className, children, ...props }: MdComponentProps) => (
    <code
      className={cn(
        "bg-neutral-900/80 border border-neutral-700/50 rounded-md px-2 py-1 font-mono text-xs text-emerald-300",
        className
      )}
      {...props}
    >
      {children}
    </code>
  ),
  pre: ({ className, children, ...props }: MdComponentProps) => (
    <pre
      className={cn(
        "bg-neutral-900/80 border border-neutral-700/50 p-4 rounded-lg overflow-x-auto font-mono text-xs my-4 text-emerald-300",
        className
      )}
      {...props}
    >
      {children}
    </pre>
  ),
  hr: ({ className, ...props }: MdComponentProps) => (
    <hr className={cn("border-neutral-600 my-4", className)} {...props} />
  ),
  table: ({ className, children, ...props }: MdComponentProps) => (
    <div className="my-3 overflow-x-auto">
      <table className={cn("border-collapse w-full", className)} {...props}>
        {children}
      </table>
    </div>
  ),
  th: ({ className, children, ...props }: MdComponentProps) => (
    <th
      className={cn(
        "border border-neutral-600 px-3 py-2 text-left font-bold",
        className
      )}
      {...props}
    >
      {children}
    </th>
  ),
  td: ({ className, children, ...props }: MdComponentProps) => (
    <td
      className={cn("border border-neutral-600 px-3 py-2", className)}
      {...props}
    >
      {children}
    </td>
  ),
};

// Props for HumanMessageBubble
interface HumanMessageBubbleProps {
  message: Message;
  mdComponents: typeof mdComponents;
}

// HumanMessageBubble Component
const HumanMessageBubble: React.FC<HumanMessageBubbleProps> = ({
  message,
  mdComponents,
}) => {
  return (
    <div className="flex items-end gap-3 max-w-[85%] md:max-w-[75%] ml-auto">
      <div
        className="text-white rounded-2xl break-words min-h-7 bg-gradient-to-br from-blue-600 to-blue-700 shadow-lg hover:shadow-xl transition-all duration-300 px-5 py-3 rounded-br-md relative group"
      >
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-transparent rounded-2xl rounded-br-md opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        <div className="relative z-10">
          <ReactMarkdown components={mdComponents}>
            {typeof message.content === "string"
              ? message.content
              : JSON.stringify(message.content)}
          </ReactMarkdown>
        </div>
        {/* Message tail */}
        <div className="absolute -bottom-0 -right-0 w-4 h-4 bg-gradient-to-br from-blue-600 to-blue-700 transform rotate-45 translate-x-2 translate-y-2" />
      </div>
    </div>
  );
};

// Props for AiMessageBubble
interface AiMessageBubbleProps {
  message: Message;
  historicalActivity: ProcessedEvent[] | undefined;
  liveActivity: ProcessedEvent[] | undefined;
  isLastMessage: boolean;
  isOverallLoading: boolean;
  mdComponents: typeof mdComponents;
  handleCopy: (text: string, messageId: string) => void;
  copiedMessageId: string | null;
}

// AiMessageBubble Component
const AiMessageBubble: React.FC<AiMessageBubbleProps> = ({
  message,
  historicalActivity,
  liveActivity,
  isLastMessage,
  isOverallLoading,
  mdComponents,
  handleCopy,
  copiedMessageId,
}) => {
  // Determine which activity events to show and if it's for a live loading message
  const activityForThisBubble =
    isLastMessage && isOverallLoading ? liveActivity : historicalActivity;
  const isLiveActivityForThisBubble = isLastMessage && isOverallLoading;

  return (
    <div className="flex items-start gap-4 max-w-[95%] md:max-w-[85%]">
      {/* AI Avatar */}
      <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-br from-emerald-500 to-emerald-600 flex items-center justify-center shadow-lg">
        <div className="w-4 h-4 rounded-full bg-white/90" />
      </div>

      {/* Message Content */}
      <div className="flex-1 min-w-0">
        <div className="bg-gradient-to-br from-neutral-800 to-neutral-900 rounded-2xl rounded-tl-md shadow-lg hover:shadow-xl transition-all duration-300 p-5 relative group border border-neutral-700/50">
          <div className="absolute inset-0 bg-gradient-to-br from-neutral-700/10 to-transparent rounded-2xl rounded-tl-md opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

          {/* Activity Timeline */}
          {activityForThisBubble && activityForThisBubble.length > 0 && (
            <div className="mb-4 border-b border-neutral-700/50 pb-4">
              <ActivityTimeline
                processedEvents={activityForThisBubble}
                isLoading={isLiveActivityForThisBubble}
              />
            </div>
          )}

          {/* Message Content */}
          <div className="relative z-10 text-neutral-100">
            <ReactMarkdown components={mdComponents}>
              {typeof message.content === "string"
                ? message.content
                : JSON.stringify(message.content)}
            </ReactMarkdown>
          </div>

          {/* Copy Button */}
          <div className="flex justify-end mt-4 pt-3 border-t border-neutral-700/30">
            <Button
              variant="outline"
              size="sm"
              className="bg-neutral-700/50 hover:bg-neutral-600/50 border-neutral-600/50 text-neutral-300 hover:text-white transition-all duration-200 text-xs gap-2"
              onClick={() => {
                console.log('Copy button clicked for message:', message.id);
                const textToCopy = typeof message.content === "string"
                  ? message.content
                  : JSON.stringify(message.content);
                console.log('Text to copy:', textToCopy.substring(0, 100) + '...');
                handleCopy(textToCopy, message.id || `msg-${Date.now()}`);
              }}
            >
              {copiedMessageId === message.id ? (
                <>
                  <CopyCheck className="h-3 w-3 text-green-400" />
                  <span className="text-green-400">Copied!</span>
                </>
              ) : (
                <>
                  <Copy className="h-3 w-3" />
                  Copy
                </>
              )}
            </Button>
          </div>

          {/* Message tail */}
          <div className="absolute -top-0 -left-0 w-4 h-4 bg-gradient-to-br from-neutral-800 to-neutral-900 transform rotate-45 -translate-x-2 -translate-y-2 border-l border-t border-neutral-700/50" />
        </div>
      </div>
    </div>
  );
};

interface ChatMessagesViewProps {
  messages: Message[];
  isLoading: boolean;
  scrollAreaRef: React.RefObject<HTMLDivElement | null>;
  onSubmit: (inputValue: string, effort: string, model: string) => void;
  onCancel: () => void;
  liveActivityEvents: ProcessedEvent[];
  historicalActivities: Record<string, ProcessedEvent[]>;
}

export function ChatMessagesView({
  messages,
  isLoading,
  scrollAreaRef,
  onSubmit,
  onCancel,
  liveActivityEvents,
  historicalActivities,
}: ChatMessagesViewProps) {
  const [copiedMessageId, setCopiedMessageId] = useState<string | null>(null);

  const handleCopy = async (text: string, messageId: string) => {
    try {
      // Check if clipboard API is available
      if (!navigator.clipboard) {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
          document.execCommand('copy');
          setCopiedMessageId(messageId);
          setTimeout(() => setCopiedMessageId(null), 2000);
          console.log('Text copied using fallback method');
        } catch (fallbackErr) {
          console.error('Fallback copy failed:', fallbackErr);
          alert('Copy failed. Please select and copy the text manually.');
        } finally {
          document.body.removeChild(textArea);
        }
        return;
      }

      // Modern clipboard API
      await navigator.clipboard.writeText(text);
      setCopiedMessageId(messageId);
      setTimeout(() => setCopiedMessageId(null), 2000);
      console.log('Text copied successfully');
    } catch (err) {
      console.error("Failed to copy text: ", err);

      // Try fallback method if modern API fails
      try {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        document.execCommand('copy');
        setCopiedMessageId(messageId);
        setTimeout(() => setCopiedMessageId(null), 2000);
        console.log('Text copied using fallback after API failure');
        document.body.removeChild(textArea);
      } catch (fallbackErr) {
        console.error('All copy methods failed:', fallbackErr);
        alert('Copy failed. Please select and copy the text manually.');
      }
    }
  };

  return (
    <div className="flex flex-col h-full">
      <ScrollArea className="flex-grow" ref={scrollAreaRef}>
        <div className="p-4 md:p-8 space-y-6 max-w-5xl mx-auto pt-20 pb-8">
          {messages.map((message, index) => {
            const isLast = index === messages.length - 1;
            return (
              <div
                key={message.id || `msg-${index}`}
                className="space-y-4 animate-fade-in"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div
                  className={`flex items-start gap-4 ${
                    message.type === "human" ? "justify-end" : ""
                  }`}
                >
                  {message.type === "human" ? (
                    <HumanMessageBubble
                      message={message}
                      mdComponents={mdComponents}
                    />
                  ) : (
                    <AiMessageBubble
                      message={message}
                      historicalActivity={historicalActivities[message.id!]}
                      liveActivity={liveActivityEvents} // Pass global live events
                      isLastMessage={isLast}
                      isOverallLoading={isLoading} // Pass global loading state
                      mdComponents={mdComponents}
                      handleCopy={handleCopy}
                      copiedMessageId={copiedMessageId}
                    />
                  )}
                </div>
              </div>
            );
          })}
          {isLoading &&
            (messages.length === 0 ||
              messages[messages.length - 1].type === "human") && (
              <div className="flex items-start gap-4 max-w-[95%] md:max-w-[85%] animate-fade-in">
                {/* AI Avatar */}
                <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-br from-emerald-500 to-emerald-600 flex items-center justify-center shadow-lg animate-pulse">
                  <div className="w-4 h-4 rounded-full bg-white/90" />
                </div>

                {/* Loading Message Content */}
                <div className="flex-1 min-w-0">
                  <div className="bg-gradient-to-br from-neutral-800 to-neutral-900 rounded-2xl rounded-tl-md shadow-lg p-5 relative border border-neutral-700/50 min-h-[80px]">
                    {liveActivityEvents.length > 0 ? (
                      <div className="text-xs">
                        <ActivityTimeline
                          processedEvents={liveActivityEvents}
                          isLoading={true}
                        />
                      </div>
                    ) : (
                      <div className="flex items-center justify-start h-full">
                        <Loader2 className="h-5 w-5 animate-spin text-blue-400 mr-3" />
                        <span className="text-neutral-200 font-medium">Processing your request...</span>
                      </div>
                    )}

                    {/* Message tail */}
                    <div className="absolute -top-0 -left-0 w-4 h-4 bg-gradient-to-br from-neutral-800 to-neutral-900 transform rotate-45 -translate-x-2 -translate-y-2 border-l border-t border-neutral-700/50" />
                  </div>
                </div>
              </div>
            )}
        </div>
      </ScrollArea>
      <InputForm
        onSubmit={onSubmit}
        isLoading={isLoading}
        onCancel={onCancel}
        hasHistory={messages.length > 0}
      />
    </div>
  );
}
